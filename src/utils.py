"""
Utility functions for the drug-disease repurposing ensemble model.
Contains helper functions for data processing, evaluation, and model handling.
"""

import os
import time
import numpy as np
import polars as pl
from tqdm import tqdm
from itertools import product
from sklearn.metrics import (
    precision_score, recall_score, f1_score,
    average_precision_score, log_loss
)
from scipy import sparse
import logging
from config import LOGS_DIR, DEFAULT_FEATURE_COL, DATA_DIR
import pickle

# Module-level logger for utils.py itself
# Create a basic logger; setup_logger can be used by other modules to get more specific loggers.
_utils_logger_instance = logging.getLogger(__name__) # Use __name__ for the logger name
if not _utils_logger_instance.hasHandlers(): # Avoid adding multiple handlers if this is re-run/re-imported
    _utils_log_file = os.path.join(LOGS_DIR, "utils_internal.log")
    os.makedirs(os.path.dirname(_utils_log_file), exist_ok=True)
    _handler = logging.FileHandler(_utils_log_file)
    _formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    _handler.setFormatter(_formatter)
    _utils_logger_instance.addHandler(_handler)
    _utils_logger_instance.setLevel(logging.INFO) 
    # Add a stream handler for console output too for this internal logger
    _console_handler = logging.StreamHandler()
    _console_handler.setFormatter(_formatter)
    _utils_logger_instance.addHandler(_console_handler)

# Set up logging
def setup_logger(name, log_file, level=logging.INFO):
    """Function to set up a logger with a file handler and formatter."""
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    handler = logging.FileHandler(log_file)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    
    logger = logging.getLogger(name)
    logger.setLevel(level)
    # Avoid adding duplicate handlers if logger already configured (e.g. in notebooks)
    if not logger.hasHandlers():
        logger.addHandler(handler)
        # Add console handler if not already present from a root config
        # This can be tricky; for now, let's assume we want it if no handlers exist at all
        console = logging.StreamHandler()
        console.setFormatter(formatter)
        logger.addHandler(console)
    elif not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
        # If file handler was added but no console, add console
        console = logging.StreamHandler()
        console.setFormatter(formatter)
        logger.addHandler(console)

    return logger

def get_timestamp():
    """Generate a timestamp string for file naming."""
    return time.strftime("%Y%m%d_%H%M%S")

def combine_embeddings(drug_emb, disease_emb):
    """
    Combine drug and disease embeddings into a single feature vector.
    
    Args:
        drug_emb (np.array): Drug embedding vector
        disease_emb (np.array): Disease embedding vector
        
    Returns:
        np.array: Combined feature vector
        
    Raises:
        ValueError: If either drug_emb or disease_emb is None.
    """
    if drug_emb is None or disease_emb is None:
        # Log this event before raising, using the logger from this module if available
        # For now, directly raise the error.
        # logger.error("Attempted to combine None embeddings for a drug-disease pair.")
        raise ValueError("Drug or disease embedding is None, cannot combine.")

    return np.concatenate([drug_emb, disease_emb])

# Evaluation metrics


def pr_auc_score(y_true, y_score):
    """Calculate Precision-Recall AUC."""
    if not isinstance(y_true, np.ndarray): 
        y_true = np.array(y_true)
    if not isinstance(y_score, np.ndarray): 
        y_score = np.array(y_score)
    return average_precision_score(y_true, y_score)

def custom_log_loss(y_true, y_pred_proba):
    """Calculate LogLoss, clipping probabilities for stability."""
    if not isinstance(y_true, np.ndarray): 
        y_true = np.array(y_true)
    if not isinstance(y_pred_proba, np.ndarray): 
        y_pred_proba = np.array(y_pred_proba)
    
    # Clip probabilities to avoid log(0) or log(1) issues
    epsilon = 1e-15
    y_pred_proba_clipped = np.clip(y_pred_proba, epsilon, 1 - epsilon)
    
    return log_loss(y_true, y_pred_proba_clipped)


def evaluate_predictions(drug_disease_data, threshold=0.5):
    """
    Comprehensive evaluation function for both classification and ranking metrics.
    """
    results = {}
    
    # Classification metrics (uses threshold)
    y_pred_proba = drug_disease_data.filter(pl.col("is_known_positive") | pl.col("is_known_negative")).select("treat score").to_numpy().flatten()
    y_true = drug_disease_data.filter(
        pl.col("is_known_positive") | pl.col("is_known_negative")
    ).with_columns(
        pl.when(pl.col("is_known_positive"))
        .then(1)
        .when(pl.col("is_known_negative"))
        .then(0)
        .otherwise(None)
        .alias("label")
    ).select("label").to_numpy().flatten()
    
    y_pred_binary = (y_pred_proba >= threshold).astype(int)
    results["accuracy"] = np.mean(y_true == y_pred_binary)
    results["precision"] = precision_score(y_true, y_pred_binary, zero_division=0)
    results["recall"] = recall_score(y_true, y_pred_binary, zero_division=0)
    results["f1"] = f1_score(y_true, y_pred_binary, zero_division=0)
    
    # Threshold-free metrics
    results["pr_auc"] = pr_auc_score(y_true, y_pred_proba)
    results["logloss"] = custom_log_loss(y_true, y_pred_proba)
    
    # MRR
    results["mrr"] = give_disease_specific_mrr(drug_disease_data, "is_known_positive", "treat score")
    
    # Hit@K
    hit_at_k = give_hit_at_k(drug_disease_data, k_max=100, bool_test_col="is_known_positive", score_col="treat score")
    for i in [1, 5, 10, 20, 100]:
        # Find the largest k value that is <= i
        valid_rows = hit_at_k.filter(pl.col("k") <= i)
        if len(valid_rows) > 0:
            # Get the row with the maximum k value among valid rows
            max_k_row = valid_rows.filter(pl.col("k") == valid_rows["k"].max())
            results[f"hit@{i}"] = max_k_row["hit_at_k"].item()
        else:
            # No valid k found, set to 0
            results[f"hit@{i}"] = 0.0     
    
    # Recall@n
    recall_lst = give_recall_at_n(drug_disease_data, [20000, 40000, 60000, 80000, 100000], "is_known_positive", "treat score")
    for i, n in enumerate([20000, 40000, 60000, 80000, 100000]):
        results[f"recall@{n}"] = recall_lst[i]
    
    return results



def load_auxiliary_data_for_evaluation():
    """
    Load auxiliary data needed for drug-disease recommendation metrics.
    
    Returns:
        tuple: (all_drug_list, all_disease_list, known_positive_pairs_set)
    """
    
    # Load all drug IDs
    with open(os.path.join(DATA_DIR, "processed_data", "all_drug_ids.pkl"), "rb") as f:
        all_drug_list = pickle.load(f)
    
    # Load all disease IDs  
    with open(os.path.join(DATA_DIR, "processed_data", "all_disease_ids.pkl"), "rb") as f:
        all_disease_list = pickle.load(f)
        
    # Load known pairs
    with open(os.path.join(DATA_DIR, "processed_data", "all_known_pairs_dict.pkl"), "rb") as f:
        known_pairs_dict = pickle.load(f)

    # Load node embedding dictionary
    with open(os.path.join(DATA_DIR, "processed_data", "node_embedding_dict.pkl"), "rb") as f:
        node_embedding_dict = pickle.load(f)

    return all_drug_list, all_disease_list, known_pairs_dict, node_embedding_dict


def prepare_drug_disease_evaluation_data(df_pl, use_enhanced_embeddings=True, k=5, exp_lambda=0.7):
    """
    Prepare drug-disease specific data for evaluation metrics.
    
    Args:
        df_pl (pl.DataFrame): DataFrame containing at least drug_id, dis_id, and label columns
        use_enhanced_embeddings (bool): Whether to use enhanced embeddings with similarity-based neighborhoods
        k (int): Number of top similar nodes to use for enhanced embeddings
        exp_lambda (float): Lambda parameter for exponential rarity weighting
    """
    # Load auxiliary data
    all_drug_list, all_disease_list, known_pairs_dict, node_embedding_dict = load_auxiliary_data_for_evaluation()

    # Create all combinations using cross product with progress tracking
    print(f"Creating {len(all_drug_list) * len(all_disease_list):,} drug-disease combinations...")
    
    # Use numpy arrays for faster operations
    all_drug_array = np.array(all_drug_list)
    all_disease_array = np.array(all_disease_list)

    # Create combinations in chunks to track progress and reduce memory usage
    chunk_size = 1000000
    
    drug_combinations = []
    dis_combinations = []
    
    with tqdm(total=len(all_drug_list), desc="Creating drug-disease combinations") as pbar:
        for i in range(0, len(all_drug_list), max(1, len(all_drug_list) // 100)):
            end_idx = min(i + max(1, len(all_drug_list) // 100), len(all_drug_list))
            drug_chunk = all_drug_array[i:end_idx]
            
            # Create combinations for this chunk
            drug_chunk_repeated = np.repeat(drug_chunk, len(all_disease_list))
            dis_chunk_tiled = np.tile(all_disease_array, len(drug_chunk))
            
            drug_combinations.extend(drug_chunk_repeated)
            dis_combinations.extend(dis_chunk_tiled)
            
            pbar.update(end_idx - i)
    
    all_pairs_df = pl.DataFrame({
        'drug_id': drug_combinations,
        'dis_id': dis_combinations
    })
    
    # Create a set of (drug_id, dis_id) pairs from df_pl for fast lookup
    df_pairs_set = set(
        zip(df_pl['drug_id'].to_list(), df_pl['dis_id'].to_list())
    )
    
    # Vectorized check for known positive/negative pairs with progress tracking
    print("Checking known positive/negative pairs...")
    all_pairs_tuples = list(zip(drug_combinations, dis_combinations))
    
    # Process in chunks for memory efficiency and progress tracking
    chunk_size = 1000000
    is_known_positive = []
    is_known_negative = []
    is_in_df = []
    
    pos_pairs_set = known_pairs_dict['pos']
    neg_pairs_set = known_pairs_dict['neg']
    
    with tqdm(total=len(all_pairs_tuples), desc="Processing pair classifications") as pbar:
        for i in range(0, len(all_pairs_tuples), chunk_size):
            chunk = all_pairs_tuples[i:i + chunk_size]
            
            # Vectorized operations on chunk
            chunk_pos = [pair in pos_pairs_set for pair in chunk]
            chunk_neg = [pair in neg_pairs_set for pair in chunk]
            chunk_in_df = [pair in df_pairs_set for pair in chunk]
            
            is_known_positive.extend(chunk_pos)
            is_known_negative.extend(chunk_neg)
            is_in_df.extend(chunk_in_df)
            
            pbar.update(len(chunk))
    
    # Add boolean columns
    all_pairs_df = all_pairs_df.with_columns([
        pl.Series('is_known_positive', is_known_positive),
        pl.Series('is_known_negative', is_known_negative),
        pl.Series('is_in_df', is_in_df)
    ])
    
    # Filter out known positive/negative pairs that are not in df_pl
    print("Filtering pairs...")
    all_pairs_df = all_pairs_df.filter(
        ~(
            # Exclude known positives not in df_pl
            (pl.col('is_known_positive') & ~pl.col('is_in_df')) |
            # Exclude known negatives not in df_pl  
            (pl.col('is_known_negative') & ~pl.col('is_in_df'))
        )
    ).drop('is_in_df')

    if use_enhanced_embeddings:
        print("Using ENHANCED embeddings with similarity-based neighborhoods...")
        # Load additional data needed for enhanced embeddings
        
        # Load bidirectional knowledge graph edges
        bi_kg_edge_path = os.path.join(DATA_DIR, "cached", "bi_kg_edge.parquet")
        if not os.path.exists(bi_kg_edge_path):
            raise FileNotFoundError(f"bi_kg_edge.parquet not found at {bi_kg_edge_path}. Required for enhanced embeddings.")
        bi_kg_edge = pl.read_parquet(bi_kg_edge_path)
        
        # Load idx_map
        idx_map_path = os.path.join(DATA_DIR, "cached", "idx_map.pkl")
        if not os.path.exists(idx_map_path):
            raise FileNotFoundError(f"idx_map.pkl not found at {idx_map_path}. Required for enhanced embeddings.")
        with open(idx_map_path, "rb") as f:
            idx_map = pickle.load(f)
        
        # Use the optimized batch enhanced embeddings function
        _utils_logger_instance.info(f"Creating enhanced embeddings with k={k}, lambda={exp_lambda}")
        enhanced_embeddings, valid_indices = create_enhanced_embeddings_batch_optimized(
            all_pairs_df, bi_kg_edge, idx_map, node_embedding_dict, k=k, exp_lambda=exp_lambda
        )
        
        print(f"Enhanced embeddings generated for {len(valid_indices)}/{all_pairs_df.shape[0]} pairs ({len(valid_indices)/all_pairs_df.shape[0]*100:.1f}%)")
        
        # Filter to only include pairs with valid enhanced embeddings
        valid_pairs_df = all_pairs_df[valid_indices]
        
        # Add embedding columns
        all_pairs_with_embeddings = valid_pairs_df.with_columns([
            pl.Series(DEFAULT_FEATURE_COL, enhanced_embeddings)
        ])
        
        _utils_logger_instance.info(f"Returning {all_pairs_with_embeddings.shape[0]} pairs with enhanced embeddings")
        
    else:
        print("Using BASIC embeddings (simple concatenation)...")
        # Basic embedding combination
        remaining_pairs = all_pairs_df.shape[0]
        drug_ids_list = all_pairs_df['drug_id'].to_list()
        dis_ids_list = all_pairs_df['dis_id'].to_list()
        
        drug_embeddings = []
        dis_embeddings = []
        
        with tqdm(total=remaining_pairs, desc="Looking up basic embeddings") as pbar:
            for i in range(0, remaining_pairs, chunk_size):
                end_idx = min(i + chunk_size, remaining_pairs)
                
                # Process chunk of drug and disease IDs
                drug_chunk = drug_ids_list[i:end_idx]
                dis_chunk = dis_ids_list[i:end_idx]
                
                drug_emb_chunk = [node_embedding_dict.get(drug_id) for drug_id in drug_chunk]
                dis_emb_chunk = [node_embedding_dict.get(dis_id) for dis_id in dis_chunk]
                
                drug_embeddings.extend(drug_emb_chunk)
                dis_embeddings.extend(dis_emb_chunk)
                
                pbar.update(end_idx - i)
        
        # Vectorized embedding combination with progress tracking
        print("Combining basic embeddings...")
        combined_embeddings = []
        valid_indices = []
        
        with tqdm(total=len(drug_embeddings), desc="Combining basic embeddings") as pbar:
            for i in range(0, len(drug_embeddings), chunk_size):
                end_idx = min(i + chunk_size, len(drug_embeddings))
                
                for j in range(i, end_idx):
                    drug_emb = drug_embeddings[j]
                    dis_emb = dis_embeddings[j]
                    
                    if drug_emb is not None and dis_emb is not None:
                        combined_embeddings.append(list(combine_embeddings(np.array(drug_emb), np.array(dis_emb))))
                        valid_indices.append(j)
                
                pbar.update(end_idx - i)
        
        print(f"Basic embeddings generated for {len(valid_indices)}/{all_pairs_df.shape[0]} pairs ({len(valid_indices)/all_pairs_df.shape[0]*100:.1f}%)")
        
        # Filter to only include pairs with valid basic embeddings
        valid_pairs_df = all_pairs_df[valid_indices]
        
        # Add embedding columns
        all_pairs_with_embeddings = valid_pairs_df.with_columns([
            pl.Series(DEFAULT_FEATURE_COL, combined_embeddings)
        ])
        
        _utils_logger_instance.info(f"Returning {all_pairs_with_embeddings.shape[0]} pairs with basic embeddings")
    
    return all_pairs_with_embeddings


# ---
# Disease-specific ranking 
# ---

def give_hit_at_k(
        matrix : pl.DataFrame, k_max : int, bool_test_col : str = "is_known_positive", score_col : str = "treat score",
        ):
    """
    Returns the hit@k score for a list of k values.

    Args:   
        matrix: Dataframe of drug-disease pairs with treat scores.
            Training set should have been taken out of the matrices.
        k_max: Maximum k value to compute hit@k for
        bool_test_col: Boolean column in the matrix indicating the known positive test set 
        score_col: Column in the matrix containing the treat scores.
    Returns:
        A dataframe with the hit@k scores and the k values.
    """
    # Restrict to test diseases
    test_diseases = matrix.group_by("dis_id").agg(pl.col(bool_test_col).sum().alias("num_known_positives")).filter(pl.col("num_known_positives") > 0).select(pl.col("dis_id")).to_series().to_list()
    matrix = matrix.filter(pl.col("dis_id").is_in(test_diseases))

    # Add disease-specific ranks
    matrix = matrix.with_columns(disease_rank=pl.col("treat score").rank(descending=True, method="random").over("dis_id"))

    #  Remove other positives from ranking
    matrix = matrix.filter(pl.col(bool_test_col)).with_columns(disease_rank_among_positives=pl.col(score_col).rank(descending=True, method="dense").over("dis_id"))
    matrix = matrix.with_columns(disease_rank_against_negatives= pl.col("disease_rank") - pl.col("disease_rank_among_positives") + 1)

    # Count number of positives at each rank and cumulative sum
    ranks_for_test_set  = matrix.filter(pl.col(bool_test_col)).group_by("disease_rank_against_negatives").len().sort("disease_rank_against_negatives")
    ranks_for_test_set = ranks_for_test_set.with_columns(pl.col("len").cum_sum().alias("cumulative_len"))

    # Compute hit@k for each k
    df_hit_at_k = pl.DataFrame(
        {
            "k": ranks_for_test_set["disease_rank_against_negatives"], 
            "hit_at_k": ranks_for_test_set["cumulative_len"] / len(matrix.filter(pl.col(bool_test_col)))
        }
    )
    
    return df_hit_at_k.filter(pl.col("k") <= k_max)


def give_disease_specific_mrr(
        matrix : pl.DataFrame, bool_test_col : str = "is_known_positive", score_col : str = "treat score",
        ):
    """
    Returns the MRR score for disease-specific ranking.

    Args:   
        matrix: Dataframe of drug-disease pairs with treat scores.
            Training set should have been taken out of the matrices.
        bool_test_col: Boolean column in the matrix indicating the known positive test set 
        score_col: Column in the matrix containing the treat scores.
    Returns:
        The MRR score for disease-specific ranking.
    """
    # Restrict to test diseases
    test_diseases = matrix.group_by("dis_id").agg(pl.col(bool_test_col).sum().alias("num_known_positives")).filter(pl.col("num_known_positives") > 0).select(pl.col("dis_id")).to_series().to_list()
    matrix = matrix.filter(pl.col("dis_id").is_in(test_diseases))

    # Add disease-specific ranks
    matrix = matrix.with_columns(disease_rank=pl.col("treat score").rank(descending=True, method="random").over("dis_id"))

    #  Remove other positives from ranking
    matrix = matrix.filter(pl.col(bool_test_col)).with_columns(disease_rank_among_positives=pl.col(score_col).rank(descending=True, method="dense").over("dis_id"))
    matrix = matrix.with_columns(disease_rank_against_negatives= pl.col("disease_rank") - pl.col("disease_rank_among_positives") + 1)

    return (1 / matrix["disease_rank_against_negatives"]).mean()

# ---
# Full matrix ranking 
# ---

def give_recall_at_n(
        matrix : pl.DataFrame,
        n_lst : list[int], 
        bool_test_col : str = "is_known_positive", 
        score_col : str = "treat score", 
        perform_sort : bool = True,
        out_of_matrix_mode : bool = False
        ):
    """
    Returns the recall@n score for a list of n values.

    Args:
        matrix: Dataframe of drug-disease pairs with treat scores.
            Training set should have been taken out of the matrices.
        n_lst: List of n values to calculate the recall@n score for.
        bool_test_col: Boolean column in the matrix indicating the known positive test set 
        score_col: Column in the matrix containing the treat scores.
        perform_sort: Whether to sort the matrix by the treat score, or expect the dataframe to be sorted already.
        out_of_matrix_mode: Whether to use the out of matrix mode, where pairs outside the matrix may be used in the calculation.
            In this case, the matrix dataframe must also contain a boolean column "in_matrix".
    Returns:
        A list of recall@n scores for the list of n values.
    """
    # Number of known positives
    N = len(matrix.filter(pl.col(bool_test_col)))
    if N == 0:
        return [0] * len(n_lst)

    if out_of_matrix_mode:
        matrix = matrix.filter(pl.col("in_matrix") | pl.col(bool_test_col))

    # Sort by treat score
    if perform_sort or out_of_matrix_mode:
        matrix = matrix.sort(by=score_col, descending=True)

    # Ranks of the known positives
    ranks_series = matrix.with_row_index("index").filter(pl.col(bool_test_col)).select(pl.col("index")).to_series() + 1
    
    # Recall@n scores
    recall_lst = [(ranks_series <= n).sum()/N for n in n_lst]

    return recall_lst


# ---
# Meta-feature creation functions for multi-level stacking
# ---

def entropy_based_confidence(probabilities):
    """Calculate confidence based on entropy. Lower entropy = higher confidence."""
    # Ensure probabilities are valid (between 0 and 1)
    probabilities = np.clip(probabilities, 1e-15, 1 - 1e-15)
    
    # Calculate binary entropy for each prediction
    entropies = -probabilities * np.log2(probabilities) - (1 - probabilities) * np.log2(1 - probabilities)
    
    # Convert entropy to confidence (lower entropy = higher confidence)
    confidence = 1 - entropies  # Since max entropy for binary is 1
    return confidence

def consensus_based_confidence(classifier_probas):
    """High agreement between models = high confidence."""
    # Standard deviation: low std = high agreement = high confidence
    std_scores = np.std(classifier_probas, axis=1)
    confidence = 1 / (1 + std_scores)  # Convert std to confidence score
    return confidence

def create_enhanced_meta_features(pred_df):
    """Create enhanced meta-features including interactions and transformations."""

    logger = logging.getLogger(__name__)

    # Define the required columns for base model probabilities
    required_base_cols = [
        'xgb_clf_proba', 'lgbm_clf_proba', 'catboost_clf_proba', 'tabnet_clf_proba'
    ]

    # Start with basic probabilities
    enhanced_features = required_base_cols.copy()
    result_df = pred_df.select(['*'])
    
    # Get probability array for statistical calculations
    proba_array = pred_df.select(required_base_cols).to_numpy()
    
    # Add statistical aggregations
    # Calculate median manually since pl.median_horizontal doesn't exist
    median_proba = np.median(proba_array, axis=1)
    std_scores = np.std(proba_array, axis=1)
    var_scores = np.var(proba_array, axis=1)
    
    result_df = result_df.with_columns([
        pl.mean_horizontal(required_base_cols).alias('mean_proba'),
        pl.lit(median_proba).alias('median_proba'),
        pl.max_horizontal(required_base_cols).alias('max_proba'),
        pl.min_horizontal(required_base_cols).alias('min_proba')
    ])
    enhanced_features.extend(['mean_proba', 'median_proba', 'max_proba', 'min_proba'])
    
    # Add variance and std
    result_df = result_df.with_columns([
        pl.lit(std_scores).alias('std_proba'),
        pl.lit(var_scores).alias('var_proba')
    ])
    enhanced_features.extend(['std_proba', 'var_proba'])
    
    # Add pairwise products (interaction features)
    for i, col1 in enumerate(required_base_cols):
        for j, col2 in enumerate(required_base_cols):
            if i < j:  # Avoid duplicate pairs
                interaction_name = f'{col1}_x_{col2}'.replace('_clf_proba', '')
                result_df = result_df.with_columns(
                    (pl.col(col1) * pl.col(col2)).alias(interaction_name)
                )
                enhanced_features.append(interaction_name)

    # Add consensus confidence (agreement between all models)
    consensus_conf = consensus_based_confidence(proba_array)
    result_df = result_df.with_columns(pl.lit(consensus_conf).alias('consensus_confidence'))
    enhanced_features.append('consensus_confidence')
    
    # Add individual model confidences
    for i, col in enumerate(required_base_cols):
        conf_col_name = f'{col}_confidence'.replace('_clf_proba', '')
        model_conf = entropy_based_confidence(proba_array[:, i])
        result_df = result_df.with_columns(pl.lit(model_conf).alias(conf_col_name))
        enhanced_features.append(conf_col_name)
    
    logger.info(f"Created {len(enhanced_features)} enhanced meta-features")
    return result_df, enhanced_features

def improved_confidence_weighting(pred_df):
    """Better confidence-based weighting."""

    # Define the required columns for base model probabilities
    required_base_cols = [
        'xgb_clf_proba', 'lgbm_clf_proba', 'catboost_clf_proba', 'tabnet_clf_proba'
    ]
    proba_array = pred_df.select(required_base_cols).to_numpy()
    
    # Option 1: Entropy-based confidence for each model
    confidences = []
    for i in range(len(required_base_cols)):
        conf = entropy_based_confidence(proba_array[:, i])
        confidences.append(conf)
    
    # Weighted average using individual model confidences
    confidences = np.array(confidences).T  # Shape: (n_samples, n_models)
    
    # Normalize weights so they sum to 1 for each sample
    weights = confidences / np.sum(confidences, axis=1, keepdims=True)
    
    # Calculate confidence-weighted average
    final_weighted_avg = np.sum(proba_array * weights, axis=1)
    
    return pred_df.with_columns([
        pl.lit(final_weighted_avg).alias('confidence_weighted_avg')
    ])

# ---
# Additional functions
# ---


def merge_node_edges(raw_node, merge_relations, new_category) -> pl.DataFrame:

    new_nodes = raw_node.clone()
    new_nodes = new_nodes.with_columns(
        pl.when(pl.col('category').is_in(merge_relations))
        .then(pl.lit(new_category))
        .otherwise(pl.col('category'))
        .alias('category')
    )
    
    return new_nodes


def preprocess_kg(raw_node, raw_edge):
    
    print('Reseting predicates...', flush=True)

    unique_pairs_df = raw_edge.select(['subject', 'object']).unique()

    node_mappings = raw_node.select(['id', 'name', 'category'])
    
    new_edge_df = unique_pairs_df.join(
        node_mappings.rename({'id': 'subject', 'category': 'subject_category'}),
        on='subject', 
        how='left'
    ).join(
        # Join for object information
        node_mappings.rename({'id': 'object', 'category': 'object_category'}),
        on='object', 
        how='left'
    ).with_columns([
        # Create relation by concatenating categories
        (pl.col('subject_category') + '_' + pl.col('object_category')).alias('relation')
    ]).drop(['subject_category', 'object_category'])
    
    unique_relations = new_edge_df['relation'].unique().to_list()

    relations_to_keep = set()
    for relation in tqdm(unique_relations, desc="Processing relations"):
        source, target = relation.split('_')
        
        if source == target:
            # homogeneous graph - keep all
            relations_to_keep.add(relation)
        else:
            # For heterogeneous edges, only keep one direction
            reversed_relation = f"{target}_{source}"
            if reversed_relation not in relations_to_keep:
                relations_to_keep.add(relation)
    
    # Filter and deduplicate in one operation
    new_edge_df = new_edge_df.filter(
        pl.col('relation').is_in(list(relations_to_keep))
    ).unique()

    # Remove the existing drug-disease relations
    drug_types = ['Drug']
    disease_types = ['Disease']
    combinations = list(product(drug_types, disease_types)) + list(product(disease_types, drug_types))
    relations_to_remove = [f"{x[0]}_{x[1]}" for x in combinations]
    new_edge_df = new_edge_df.filter(~pl.col('relation').is_in(relations_to_remove))

    print('Creating node indices...', flush=True)

    idx_map = {}
    node_id_to_name = {}
    node_id_to_category = {}
    
    for category in tqdm(raw_node['category'].unique().to_list(), desc="Generating mappings"):
        # Use polars operations instead of python loops
        category_nodes = raw_node.filter(pl.col('category') == category).with_row_index()
        
        # Extract data in bulk
        node_ids = category_nodes['id'].to_list()
        indices = category_nodes['index'].to_list()
        names = category_nodes['name'].to_list()
        
        # Create mappings
        idx_map[category] = dict(zip(node_ids, indices))
        node_id_to_name.update(dict(zip(node_ids, names)))
        node_id_to_category.update(dict(zip(node_ids, [category] * len(node_ids))))

    # Step 6: Create index mapping DataFrames for joins
    print('Mapping node IDs to indices and types...', flush=True)
    
    # Create mapping DataFrames for subject and object indices
    subject_idx_df = pl.DataFrame([
        {'subject': node_id, 'subject_index': idx_map[category][node_id], 'subject_type': category}
        for category in idx_map
        for node_id in idx_map[category]
    ])
    
    object_idx_df = pl.DataFrame([
        {'object': node_id, 'object_index': idx_map[category][node_id], 'object_type': category}
        for category in idx_map
        for node_id in idx_map[category]
    ])
    
    # Use joins for final mapping (much faster than map_elements)
    new_edge_df = new_edge_df.join(subject_idx_df, on='subject', how='left').join(
        object_idx_df, on='object', how='left'
    )

    return new_edge_df, node_id_to_name, node_id_to_category, idx_map


def reverse_rel_generation(df):
    
    # Define the 7 essential columns we want to keep
    essential_columns = ['subject', 'relation', 'object', 'subject_index', 'object_index', 'subject_type', 'object_type']
    
    # Filter the original DataFrame to only keep essential columns
    df_filtered = df.select(essential_columns)
    
    # extract unique relations
    unique_rel = df_filtered["relation"].unique().to_list()
    
    reversed_dfs = []
    
    for rel in tqdm(unique_rel, desc='Generating reverse relations'):
        # Filter dataframe for this relation
        temp = df_filtered.filter(pl.col("relation") == rel)
        
        # Get the subject and object types for this relation
        subject_type = temp["subject_type"].unique().to_list()[0]
        object_type = temp["object_type"].unique().to_list()[0]
        
        # Create reversed relation by swapping subject/object columns
        # Make sure to maintain the same column order as essential_columns
        temp_reversed = temp.select([
            pl.col("object").alias("subject"),
            # Handle relation name based on whether it's homogeneous or heterogeneous
            pl.when(subject_type != object_type)
            .then(pl.lit('rev_' + rel))
            .otherwise(pl.lit(rel))
            .alias("relation"),
            pl.col("subject").alias("object"), 
            pl.col("object_index").alias("subject_index"),
            pl.col("subject_index").alias("object_index"),
            pl.col("object_type").alias("subject_type"),
            pl.col("subject_type").alias("object_type")
        ])
        
        reversed_dfs.append(temp_reversed)
    
    # Concatenate original dataframe with all reversed relations
    if reversed_dfs:
        all_reversed = pl.concat(reversed_dfs, how="vertical")
        result = pl.concat([df_filtered, all_reversed], how="vertical")
    else:
        result = df_filtered
    
    return result


def sim_matrix(a, b, eps=1e-8):
    """
    Compute cosine similarity matrix between two sets of vectors using NumPy or sparse matrices.
    
    Args:
        a (np.ndarray or sparse matrix): Query vectors of shape (n_queries, embedding_dim)
        b (np.ndarray or sparse matrix): Key vectors of shape (n_keys, embedding_dim)
        eps (float): Small value for numerical stability
        
    Returns:
        np.ndarray: Similarity matrix of shape (n_queries, n_keys)
    """
    # Handle sparse matrices
    if sparse.issparse(a) or sparse.issparse(b):
        # Convert to CSR format for efficient operations
        if sparse.issparse(a):
            a = a.tocsr()
        else:
            a = sparse.csr_matrix(a)
        if sparse.issparse(b):
            b = b.tocsr()
        else:
            b = sparse.csr_matrix(b)
        
        # Compute norms for sparse matrices
        a_n = np.sqrt(np.array(a.multiply(a).sum(axis=1))).flatten()  # Shape: (n_queries,)
        b_n = np.sqrt(np.array(b.multiply(b).sum(axis=1))).flatten()  # Shape: (n_keys,)
        
        # Apply epsilon for numerical stability
        a_n = np.maximum(a_n, eps)
        b_n = np.maximum(b_n, eps)
        
        # Normalize
        a_norm = a.multiply(1.0 / a_n.reshape(-1, 1))  # Broadcasting for sparse division
        b_norm = b.multiply(1.0 / b_n.reshape(-1, 1))
        
        # Compute similarity matrix (sparse dot product)
        sim_mt = a_norm.dot(b_norm.T)
        
        # Convert result to dense array
        if sparse.issparse(sim_mt):
            sim_mt = sim_mt.toarray()
        
        return sim_mt
    else:
        # Original dense implementation
        # Compute norms
        a_n = np.linalg.norm(a, axis=1, keepdims=True)  # Shape: (n_queries, 1)
        b_n = np.linalg.norm(b, axis=1, keepdims=True)  # Shape: (n_keys, 1)
        
        # Apply epsilon for numerical stability
        a_norm = a / np.maximum(a_n, eps * np.ones_like(a_n))
        b_norm = b / np.maximum(b_n, eps * np.ones_like(b_n))
        
        # Compute similarity matrix
        sim_mt = np.dot(a_norm, b_norm.T)
        return sim_mt


def exponential(x, lamb):
    """
    Compute exponential weighting based on node degrees (for rarity measure).
    This matches the exact logic of the original function.
    """
    return lamb * np.exp(-lamb * x) + 0.2


def normalize_weights(weights):
    """
    Normalize weights using L1 normalization.
    """
    if weights.ndim == 1:
        # For 1D array, normalize along the single dimension
        weights_sum = np.sum(np.abs(weights))
        if weights_sum > 0:
            return weights / weights_sum
        else:
            return weights
    else:
        # For 2D array, normalize along dim=1 (each row sums to 1)
        weights_sum = np.sum(np.abs(weights), axis=1, keepdims=True)
        weights_sum = np.where(weights_sum > 0, weights_sum, 1.0)  # Avoid division by zero
        return weights / weights_sum


# Function removed - not referenced in codebase


def precompute_drug_profiles_from_edges(bi_kg_edge, idx_map, node_embedding_dict, use_sparse=True):
    """
    Pre-compute ONLY drug profiles from edges to save memory.
    
    Args:
        bi_kg_edge (pl.DataFrame): Bidirectional KG edges
        idx_map: Mapping from node IDs to graph indices
        node_embedding_dict: Dictionary mapping node ID to embedding vector
        use_sparse (bool): Whether to use sparse matrices for profiles
        
    Returns:
        tuple: (drug_profiles_dict, drug_embeddings_dict, drug_degrees_dict)
    """
    
    # Check if cached results exist
    cache_dir = os.path.join(DATA_DIR, "cached")
    os.makedirs(cache_dir, exist_ok=True)
    
    cache_files = {
        'drug_embeddings': os.path.join(cache_dir, "drug_embeddings_dict.pkl"),
        'drug_degrees': os.path.join(cache_dir, "drug_degrees_dict.pkl")
    }

    # Check if embeddings and degrees cache files exist
    all_cached = all(os.path.exists(path) for path in cache_files.values())

    if all_cached:
        try:
            _utils_logger_instance.info("Loading drug embeddings and degrees from cache...")
            with open(cache_files['drug_embeddings'], 'rb') as f:
                drug_embeddings_dict = pickle.load(f)
            with open(cache_files['drug_degrees'], 'rb') as f:
                drug_degrees_dict = pickle.load(f)
            _utils_logger_instance.info(f"Loaded {len(drug_embeddings_dict)} drug embeddings and degrees from cache")

        except Exception as e:
            _utils_logger_instance.warning(f"Failed to load cached drug data: {e}. Recomputing...")
    
    if not all_cached:
        _utils_logger_instance.info(f"Computing DRUG profiles only (sparse={use_sparse})...")
        drug_embeddings_dict = {}
        drug_degrees_dict = {}
    else:
        _utils_logger_instance.info(f"Computing DRUG profiles only (sparse={use_sparse}) - using cached embeddings/degrees...")

    # Helper function to check if edge type exists with correct source type
    def has_edge_with_source_type(relation, source_type):
        return not bi_kg_edge.filter(
            (pl.col('relation') == relation) & (pl.col('subject_type') == source_type)
        ).is_empty()

    # Define edge types for drug profile computation
    drug_etypes = ['Drug_Drug']
    if has_edge_with_source_type('rev_Gene_Drug', 'Drug'):
        drug_etypes.append('rev_Gene_Drug')
    elif has_edge_with_source_type('Drug_Gene', 'Drug'):
        drug_etypes.append('Drug_Gene')
    if has_edge_with_source_type('rev_Protein_Drug', 'Drug'):
        drug_etypes.append('rev_Protein_Drug')
    elif has_edge_with_source_type('Drug_Protein', 'Drug'):
        drug_etypes.append('Drug_Protein')
    drug_nodes = ['Drug', 'Gene', 'Protein']

    # Count nodes per type for profile sizes
    node_counts = {}
    for node_type, type_idx_map in idx_map.items():
        node_counts[node_type] = len(type_idx_map)

    # Pre-compute edge mappings for fast lookup
    edge_mappings = {}
    for etype in drug_etypes:
        etype_edges = bi_kg_edge.filter(pl.col('relation') == etype)
        if not etype_edges.is_empty():
            edge_mappings[etype] = {
                'sources': etype_edges['subject_index'].to_numpy(),
                'targets': etype_edges['object_index'].to_numpy()
            }

    # Initialize result dictionaries
    drug_profiles_dict = {}
    
    # Process drugs
    if 'Drug' in idx_map:
        _utils_logger_instance.info(f"Computing profiles for {len(idx_map['Drug'])} drugs...")
        for drug_id, drug_idx in tqdm(idx_map['Drug'].items(), desc="Computing drug profiles"):
            drug_emb = node_embedding_dict.get(drug_id)
            if drug_emb is not None:
                # Compute drug profile
                profiles = []
                degree = 0

                for i, etype in enumerate(drug_etypes):
                    if etype in edge_mappings:
                        sources = edge_mappings[etype]['sources']
                        targets = edge_mappings[etype]['targets']

                        # Find targets for this drug
                        mask = sources == drug_idx
                        connected_nodes = targets[mask]
                        degree += len(connected_nodes)

                        # Create profile vector (sparse or dense)
                        profile_size = node_counts[drug_nodes[i]]
                        if use_sparse and len(connected_nodes) > 0:
                            profile = sparse.csr_matrix(
                                (np.ones(len(connected_nodes)), (np.zeros(len(connected_nodes)), connected_nodes)),
                                shape=(1, profile_size)
                            )
                        else:
                            profile = np.zeros(profile_size)
                            if len(connected_nodes) > 0:
                                profile[connected_nodes] = 1.0
                        profiles.append(profile)
                    else:
                        # No edges of this type
                        profile_size = node_counts[drug_nodes[i]]
                        profiles.append(np.zeros(profile_size))

                # Concatenate profiles
                if use_sparse and any(sparse.issparse(p) for p in profiles):
                    sparse_profiles = []
                    for p in profiles:
                        if sparse.issparse(p):
                            sparse_profiles.append(p)
                        else:
                            sparse_profiles.append(sparse.csr_matrix(p.reshape(1, -1)))
                    drug_profile = sparse.hstack(sparse_profiles)
                else:
                    drug_profile = np.concatenate(profiles)

                drug_profiles_dict[drug_id] = drug_profile
                # Only populate embeddings and degrees if not cached
                if not all_cached:
                    drug_embeddings_dict[drug_id] = drug_emb
                    drug_degrees_dict[drug_id] = degree

    # Save only embeddings and degrees to cache (not profiles)
    if not all_cached:
        _utils_logger_instance.info("Saving computed drug embeddings and degrees to cache...")
        try:
            with open(cache_files['drug_embeddings'], 'wb') as f:
                pickle.dump(drug_embeddings_dict, f)
            with open(cache_files['drug_degrees'], 'wb') as f:
                pickle.dump(drug_degrees_dict, f)
            _utils_logger_instance.info("Successfully saved drug embeddings and degrees to cache")
        except Exception as e:
            _utils_logger_instance.warning(f"Failed to save drug data to cache: {e}")

    _utils_logger_instance.info(f"Computed {len(drug_profiles_dict)} drug profiles (profiles not cached)")
    return drug_profiles_dict, drug_embeddings_dict, drug_degrees_dict


def precompute_disease_profiles_from_edges(bi_kg_edge, idx_map, node_embedding_dict, use_sparse=True):
    """
    Pre-compute ONLY disease profiles from edges to save memory.
    
    Args:
        bi_kg_edge (pl.DataFrame): Bidirectional KG edges
        idx_map: Mapping from node IDs to graph indices
        node_embedding_dict: Dictionary mapping node ID to embedding vector
        use_sparse (bool): Whether to use sparse matrices for profiles
        
    Returns:
        tuple: (disease_profiles_dict, disease_embeddings_dict, disease_degrees_dict)
    """
    
    # Check if cached results exist
    cache_dir = os.path.join(DATA_DIR, "cached")
    os.makedirs(cache_dir, exist_ok=True)
    
    cache_files = {
        'disease_embeddings': os.path.join(cache_dir, "disease_embeddings_dict.pkl"),
        'disease_degrees': os.path.join(cache_dir, "disease_degrees_dict.pkl")
    }

    # Check if embeddings and degrees cache files exist
    all_cached = all(os.path.exists(path) for path in cache_files.values())

    if all_cached:
        try:
            _utils_logger_instance.info("Loading disease embeddings and degrees from cache...")
            with open(cache_files['disease_embeddings'], 'rb') as f:
                disease_embeddings_dict = pickle.load(f)
            with open(cache_files['disease_degrees'], 'rb') as f:
                disease_degrees_dict = pickle.load(f)
            _utils_logger_instance.info(f"Loaded {len(disease_embeddings_dict)} disease embeddings and degrees from cache")

        except Exception as e:
            _utils_logger_instance.warning(f"Failed to load cached disease data: {e}. Recomputing...")
    
    if not all_cached:
        _utils_logger_instance.info(f"Computing DISEASE profiles only (sparse={use_sparse})...")
        disease_embeddings_dict = {}
        disease_degrees_dict = {}
    else:
        _utils_logger_instance.info(f"Computing DISEASE profiles only (sparse={use_sparse}) - using cached embeddings/degrees...")

    # Helper function to check if edge type exists with correct source type
    def has_edge_with_source_type(relation, source_type):
        return not bi_kg_edge.filter(
            (pl.col('relation') == relation) & (pl.col('subject_type') == source_type)
        ).is_empty()

    # Define edge types for disease profile computation
    disease_etypes = ['Disease_Disease']
    if has_edge_with_source_type('rev_Protein_Disease', 'Disease'):
        disease_etypes.append('rev_Protein_Disease')
    elif has_edge_with_source_type('Disease_Protein', 'Disease'):
        disease_etypes.append('Disease_Protein')
    if has_edge_with_source_type('rev_Gene_Disease', 'Disease'):
        disease_etypes.append('rev_Gene_Disease')
    elif has_edge_with_source_type('Disease_Gene', 'Disease'):
        disease_etypes.append('Disease_Gene')
    if has_edge_with_source_type('rev_Pathway_Disease', 'Disease'):
        disease_etypes.append('rev_Pathway_Disease')
    elif has_edge_with_source_type('Disease_Pathway', 'Disease'):
        disease_etypes.append('Disease_Pathway')
    disease_nodes = ['Disease', 'Protein', 'Gene', 'Pathway']

    # Count nodes per type for profile sizes
    node_counts = {}
    for node_type, type_idx_map in idx_map.items():
        node_counts[node_type] = len(type_idx_map)

    # Pre-compute edge mappings for fast lookup
    edge_mappings = {}
    for etype in disease_etypes:
        etype_edges = bi_kg_edge.filter(pl.col('relation') == etype)
        if not etype_edges.is_empty():
            edge_mappings[etype] = {
                'sources': etype_edges['subject_index'].to_numpy(),
                'targets': etype_edges['object_index'].to_numpy()
            }

    # Initialize result dictionaries
    disease_profiles_dict = {}
    
    # Process diseases
    if 'Disease' in idx_map:
        _utils_logger_instance.info(f"Computing profiles for {len(idx_map['Disease'])} diseases...")
        for disease_id, disease_idx in tqdm(idx_map['Disease'].items(), desc="Computing disease profiles"):
            disease_emb = node_embedding_dict.get(disease_id)
            if disease_emb is not None:
                # Compute disease profile
                profiles = []
                degree = 0
                
                for i, etype in enumerate(disease_etypes):
                    if etype in edge_mappings:
                        sources = edge_mappings[etype]['sources']
                        targets = edge_mappings[etype]['targets']
                        
                        # Find targets for this disease
                        mask = sources == disease_idx
                        connected_nodes = targets[mask]
                        
                        # For disease in-degree calculation
                        if etype.startswith('rev_'):
                            mask_indegree = targets == disease_idx
                            degree += np.sum(mask_indegree)
                        else:
                            degree += len(connected_nodes)
                        
                        # Create profile vector (sparse or dense)
                        profile_size = node_counts[disease_nodes[i]]
                        if use_sparse and len(connected_nodes) > 0:
                            profile = sparse.csr_matrix(
                                (np.ones(len(connected_nodes)), (np.zeros(len(connected_nodes)), connected_nodes)),
                                shape=(1, profile_size)
                            )
                        else:
                            profile = np.zeros(profile_size)
                            if len(connected_nodes) > 0:
                                profile[connected_nodes] = 1.0
                        profiles.append(profile)
                    else:
                        # No edges of this type
                        profile_size = node_counts[disease_nodes[i]]
                        profiles.append(np.zeros(profile_size))
                
                # Concatenate profiles
                if use_sparse and any(sparse.issparse(p) for p in profiles):
                    sparse_profiles = []
                    for p in profiles:
                        if sparse.issparse(p):
                            sparse_profiles.append(p)
                        else:
                            sparse_profiles.append(sparse.csr_matrix(p.reshape(1, -1)))
                    disease_profile = sparse.hstack(sparse_profiles)
                else:
                    disease_profile = np.concatenate(profiles)
                
                disease_profiles_dict[disease_id] = disease_profile
                # Only populate embeddings and degrees if not cached
                if not all_cached:
                    disease_embeddings_dict[disease_id] = disease_emb
                    disease_degrees_dict[disease_id] = degree

    # Save only embeddings and degrees to cache (not profiles)
    if not all_cached:
        _utils_logger_instance.info("Saving computed disease embeddings and degrees to cache...")
        try:
            with open(cache_files['disease_embeddings'], 'wb') as f:
                pickle.dump(disease_embeddings_dict, f)
            with open(cache_files['disease_degrees'], 'wb') as f:
                pickle.dump(disease_degrees_dict, f)
            _utils_logger_instance.info("Successfully saved disease embeddings and degrees to cache")
        except Exception as e:
            _utils_logger_instance.warning(f"Failed to save disease data to cache: {e}")

    _utils_logger_instance.info(f"Computed {len(disease_profiles_dict)} disease profiles (profiles not cached)")
    return disease_profiles_dict, disease_embeddings_dict, disease_degrees_dict


def precompute_drug_similarity_rows_to_disk(drug_profiles_dict):
    """
    Compute drug similarity matrix and save row-by-row to disk. Memory-efficient.
    
    Args:
        drug_profiles_dict: Dictionary mapping drug IDs to their profiles
        
    Returns:
        dict: drug_id_to_idx mapping
    """
    
    # Setup cache directories
    cache_dir = os.path.join(DATA_DIR, "cached")
    drug_sim_dir = os.path.join(cache_dir, "drug_similarities")
    os.makedirs(drug_sim_dir, exist_ok=True)
    
    # Create ID to index mapping
    drug_ids = list(drug_profiles_dict.keys())
    drug_id_to_idx = {drug_id: idx for idx, drug_id in enumerate(drug_ids)}
    
    # Save mapping
    mapping_file = os.path.join(cache_dir, "drug_id_to_idx.pkl")
    
    # Check if drug similarity rows already exist
    drug_rows_exist = len(drug_ids) > 0 and all(
        os.path.exists(os.path.join(drug_sim_dir, f"drug_{i}.npy")) 
        for i in range(min(10, len(drug_ids)))  # Check first 10 as sample
    )
    
    if drug_rows_exist and os.path.exists(mapping_file):
        _utils_logger_instance.info("Loading pre-computed drug similarity mappings from cache...")
        try:
            with open(mapping_file, 'rb') as f:
                drug_id_to_idx = pickle.load(f)
            _utils_logger_instance.info(f"Loaded cached drug similarity rows: {len(drug_ids)} drugs")
            return drug_id_to_idx
        except Exception as e:
            _utils_logger_instance.warning(f"Failed to load cached drug mappings: {e}. Recomputing...")

    _utils_logger_instance.info(f"Computing drug similarity matrix for {len(drug_ids)} drugs...")

    # Stack all drug profiles once
    drug_profiles_list = [drug_profiles_dict[drug_id] for drug_id in drug_ids]
    if any(sparse.issparse(p) for p in drug_profiles_list):
        sparse_profiles = []
        for p in drug_profiles_list:
            if sparse.issparse(p):
                if p.shape[0] == 1:
                    sparse_profiles.append(p)
                else:
                    sparse_profiles.append(sparse.csr_matrix(p.flatten().reshape(1, -1)))
            else:
                sparse_profiles.append(sparse.csr_matrix(p.reshape(1, -1)))
        drug_profiles_matrix = sparse.vstack(sparse_profiles)
    else:
        drug_profiles_matrix = np.array([p.flatten() if p.ndim > 1 else p for p in drug_profiles_list])

    # Compute full similarity matrix once
    _utils_logger_instance.info("Computing full drug similarity matrix...")
    drug_sim_matrix = sim_matrix(drug_profiles_matrix, drug_profiles_matrix)

    # Save each row to disk and free memory immediately
    _utils_logger_instance.info(f"Saving {len(drug_ids)} drug similarity rows to disk...")
    for i in tqdm(range(len(drug_ids)), desc="Saving drug similarity rows"):
        sim_row = drug_sim_matrix[i, :]  # Extract row
        row_file = os.path.join(drug_sim_dir, f"drug_{i}.npy")
        np.save(row_file, sim_row)
    
    # Save mapping
    try:
        with open(mapping_file, 'wb') as f:
            pickle.dump(drug_id_to_idx, f)
        _utils_logger_instance.info("Successfully saved drug similarity mappings to cache")
    except Exception as e:
        _utils_logger_instance.warning(f"Failed to save drug mappings to cache: {e}")
    
    return drug_id_to_idx


def precompute_disease_similarity_rows_to_disk(disease_profiles_dict):
    """
    Compute disease similarity matrix and save row-by-row to disk. Memory-efficient.
    
    Args:
        disease_profiles_dict: Dictionary mapping disease IDs to their profiles
        
    Returns:
        dict: disease_id_to_idx mapping
    """
    
    # Setup cache directories
    cache_dir = os.path.join(DATA_DIR, "cached")
    disease_sim_dir = os.path.join(cache_dir, "disease_similarities")
    os.makedirs(disease_sim_dir, exist_ok=True)
    
    # Create ID to index mapping
    disease_ids = list(disease_profiles_dict.keys())
    disease_id_to_idx = {disease_id: idx for idx, disease_id in enumerate(disease_ids)}
    
    # Save mapping
    mapping_file = os.path.join(cache_dir, "disease_id_to_idx.pkl")
    
    # Check if disease similarity rows already exist
    disease_rows_exist = len(disease_ids) > 0 and all(
        os.path.exists(os.path.join(disease_sim_dir, f"disease_{i}.npy")) 
        for i in range(min(10, len(disease_ids)))  # Check first 10 as sample
    )
    
    if disease_rows_exist and os.path.exists(mapping_file):
        _utils_logger_instance.info("Loading pre-computed disease similarity mappings from cache...")
        try:
            with open(mapping_file, 'rb') as f:
                disease_id_to_idx = pickle.load(f)
            _utils_logger_instance.info(f"Loaded cached disease similarity rows: {len(disease_ids)} diseases")
            return disease_id_to_idx
        except Exception as e:
            _utils_logger_instance.warning(f"Failed to load cached disease mappings: {e}. Recomputing...")

    _utils_logger_instance.info(f"Computing disease similarity matrix for {len(disease_ids)} diseases...")

    # Stack all disease profiles once
    disease_profiles_list = [disease_profiles_dict[disease_id] for disease_id in disease_ids]
    if any(sparse.issparse(p) for p in disease_profiles_list):
        sparse_profiles = []
        for p in disease_profiles_list:
            if sparse.issparse(p):
                if p.shape[0] == 1:
                    sparse_profiles.append(p)
                else:
                    sparse_profiles.append(sparse.csr_matrix(p.flatten().reshape(1, -1)))
            else:
                sparse_profiles.append(sparse.csr_matrix(p.reshape(1, -1)))
        disease_profiles_matrix = sparse.vstack(sparse_profiles)
    else:
        disease_profiles_matrix = np.array([p.flatten() if p.ndim > 1 else p for p in disease_profiles_list])

    # Compute full similarity matrix once
    _utils_logger_instance.info("Computing full disease similarity matrix...")
    disease_sim_matrix = sim_matrix(disease_profiles_matrix, disease_profiles_matrix)

    # Save each row to disk and free memory immediately
    _utils_logger_instance.info(f"Saving {len(disease_ids)} disease similarity rows to disk...")
    for i in tqdm(range(len(disease_ids)), desc="Saving disease similarity rows"):
        sim_row = disease_sim_matrix[i, :]  # Extract row
        row_file = os.path.join(disease_sim_dir, f"disease_{i}.npy")
        np.save(row_file, sim_row)
    
    # Save mapping
    try:
        with open(mapping_file, 'wb') as f:
            pickle.dump(disease_id_to_idx, f)
        _utils_logger_instance.info("Successfully saved disease similarity mappings to cache")
    except Exception as e:
        _utils_logger_instance.warning(f"Failed to save disease mappings to cache: {e}")
    
    return disease_id_to_idx


def check_similarity_matrices_exist():
    """
    Check if both drug and disease similarity matrices exist on disk.

    Returns:
        tuple: (drug_sim_exists, disease_sim_exists, drug_id_to_idx, disease_id_to_idx)
    """
    cache_dir = os.path.join(DATA_DIR, "cached")

    # Check drug similarity
    drug_mapping_file = os.path.join(cache_dir, "drug_id_to_idx.pkl")
    drug_sim_dir = os.path.join(cache_dir, "drug_similarities")
    drug_sim_exists = False
    drug_id_to_idx = None

    if os.path.exists(drug_mapping_file):
        try:
            with open(drug_mapping_file, 'rb') as f:
                drug_id_to_idx = pickle.load(f)
            # Check if similarity files exist for first 10 drugs as sample
            if len(drug_id_to_idx) > 0:
                drug_sim_exists = all(
                    os.path.exists(os.path.join(drug_sim_dir, f"drug_{i}.npy"))
                    for i in range(min(10, len(drug_id_to_idx)))
                )
        except Exception as e:
            _utils_logger_instance.warning(f"Failed to load drug mappings: {e}")

    # Check disease similarity
    disease_mapping_file = os.path.join(cache_dir, "disease_id_to_idx.pkl")
    disease_sim_dir = os.path.join(cache_dir, "disease_similarities")
    disease_sim_exists = False
    disease_id_to_idx = None

    if os.path.exists(disease_mapping_file):
        try:
            with open(disease_mapping_file, 'rb') as f:
                disease_id_to_idx = pickle.load(f)
            # Check if similarity files exist for first 10 diseases as sample
            if len(disease_id_to_idx) > 0:
                disease_sim_exists = all(
                    os.path.exists(os.path.join(disease_sim_dir, f"disease_{i}.npy"))
                    for i in range(min(10, len(disease_id_to_idx)))
                )
        except Exception as e:
            _utils_logger_instance.warning(f"Failed to load disease mappings: {e}")

    return drug_sim_exists, disease_sim_exists, drug_id_to_idx, disease_id_to_idx


def load_embeddings_and_degrees():
    """
    Load embeddings and degrees dictionaries from cache.

    Returns:
        tuple: (drug_embeddings_dict, disease_embeddings_dict, drug_degrees_dict, disease_degrees_dict)
        Returns None for any dictionary that couldn't be loaded.
    """
    cache_dir = os.path.join(DATA_DIR, "cached")

    cache_files = {
        'drug_embeddings': os.path.join(cache_dir, "drug_embeddings_dict.pkl"),
        'disease_embeddings': os.path.join(cache_dir, "disease_embeddings_dict.pkl"),
        'drug_degrees': os.path.join(cache_dir, "drug_degrees_dict.pkl"),
        'disease_degrees': os.path.join(cache_dir, "disease_degrees_dict.pkl")
    }

    results = {}

    for key, cache_file in cache_files.items():
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    results[key] = pickle.load(f)
                _utils_logger_instance.info(f"Loaded {key} from cache: {len(results[key])} entries")
            except Exception as e:
                _utils_logger_instance.warning(f"Failed to load {key} from cache: {e}")
                results[key] = None
        else:
            results[key] = None

    return (results['drug_embeddings'], results['disease_embeddings'],
            results['drug_degrees'], results['disease_degrees'])


def load_similarity_row(node_idx, node_type):
    """
    Load a specific similarity row from disk.

    Args:
        node_idx: Index of the node in the similarity matrix
        node_type: Either 'drug' or 'disease'

    Returns:
        np.ndarray: Similarity row for the specified node
    """
    from config import DATA_DIR
    import os

    cache_dir = os.path.join(DATA_DIR, "cached")

    if node_type == 'drug':
        sim_dir = os.path.join(cache_dir, "drug_similarities")
        row_file = os.path.join(sim_dir, f"drug_{node_idx}.npy")
    elif node_type == 'disease':
        sim_dir = os.path.join(cache_dir, "disease_similarities")
        row_file = os.path.join(sim_dir, f"disease_{node_idx}.npy")
    else:
        raise ValueError(f"Unknown node_type: {node_type}")

    if not os.path.exists(row_file):
        raise FileNotFoundError(f"Similarity row file not found: {row_file}")

    return np.load(row_file)


def create_enhanced_embeddings_batch_optimized(pairs_df, bi_kg_edge, idx_map, node_embedding_dict, k=5, exp_lambda=0.7):
    """
    OPTIMIZED BATCH VERSION: Pre-computes enhanced embeddings for unique drugs/diseases to eliminate redundancy.
    
    Major optimizations:
    1. Identifies unique drugs/diseases in pairs_df to avoid redundant computations
    2. Pre-computes enhanced embeddings once per unique node (not per pair)
    3. Minimizes disk I/O by batch loading similarity rows with in-memory caching
    4. Uses vectorized operations where possible
    5. Simple O(1) lookup for each pair instead of O(K) computation
    
    Args:
        pairs_df (pl.DataFrame): DataFrame with 'drug_id' and 'dis_id' columns
        bi_kg_edge (pl.DataFrame): Bidirectional knowledge graph edges
        idx_map: Mapping from node IDs to graph indices
        node_embedding_dict: Dictionary mapping node ID to embedding vector
        k (int): Number of top similar nodes to use for merging
        exp_lambda (float): Lambda parameter for exponential rarity weighting
        
    Returns:
        tuple: (enhanced_embeddings_list, valid_indices)
    """
    _utils_logger_instance.info(f"Creating BATCH-OPTIMIZED enhanced embeddings for {pairs_df.shape[0]} pairs (k={k})...")

    # Step 1: Check if similarity matrices already exist on disk
    drug_sim_exists, disease_sim_exists, drug_id_to_idx, disease_id_to_idx = check_similarity_matrices_exist()

    if drug_sim_exists and disease_sim_exists:
        _utils_logger_instance.info("Both drug and disease similarity matrices exist on disk. Loading embeddings and degrees only...")

        # Load only embeddings and degrees dictionaries
        (drug_embeddings_dict, disease_embeddings_dict,
         drug_degrees_dict, disease_degrees_dict) = load_embeddings_and_degrees()

        # Check if all required dictionaries were loaded successfully
        if (drug_embeddings_dict is not None and disease_embeddings_dict is not None and
            drug_degrees_dict is not None and disease_degrees_dict is not None):
            _utils_logger_instance.info("Successfully loaded all required dictionaries from cache. Skipping profile computation.")
        else:
            _utils_logger_instance.warning("Some dictionaries failed to load. Falling back to full computation...")
            drug_sim_exists = disease_sim_exists = False

    if not (drug_sim_exists and disease_sim_exists):
        # Step 2: Pre-compute drug profiles and similarity matrix (memory-efficient)
        _utils_logger_instance.info("Processing drugs first to save memory...")
        (drug_profiles_dict, drug_embeddings_dict, drug_degrees_dict) = precompute_drug_profiles_from_edges(
            bi_kg_edge, idx_map, node_embedding_dict
        )

        # Compute drug similarity matrix and save to disk
        drug_id_to_idx = precompute_drug_similarity_rows_to_disk(drug_profiles_dict)

        # Free drug profiles memory
        del drug_profiles_dict
        _utils_logger_instance.info("Freed drug profiles memory")

        # Step 3: Pre-compute disease profiles and similarity matrix (memory-efficient)
        _utils_logger_instance.info("Processing diseases to save memory...")
        (disease_profiles_dict, disease_embeddings_dict, disease_degrees_dict) = precompute_disease_profiles_from_edges(
            bi_kg_edge, idx_map, node_embedding_dict
        )

        # Compute disease similarity matrix and save to disk
        disease_id_to_idx = precompute_disease_similarity_rows_to_disk(disease_profiles_dict)

        # Free disease profiles memory
        del disease_profiles_dict
        _utils_logger_instance.info("Freed disease profiles memory")
    
    # Step 4: OPTIMIZATION - Find unique drugs and diseases in pairs
    unique_drugs = set(pairs_df['drug_id'].unique().to_list())
    unique_diseases = set(pairs_df['dis_id'].unique().to_list())

    _utils_logger_instance.info(f"Found {len(unique_drugs)} unique drugs and {len(unique_diseases)} unique diseases in {pairs_df.shape[0]} pairs")
    _utils_logger_instance.info(f"Optimization ratio: {pairs_df.shape[0]/(len(unique_drugs) + len(unique_diseases)):.1f}x fewer computations needed")

    # Step 5: Pre-compute enhanced embeddings for unique nodes only
    _utils_logger_instance.info("Pre-computing enhanced embeddings for unique drugs...")
    enhanced_drug_cache = {}
    drug_idx_to_id = {idx: drug_id for drug_id, idx in drug_id_to_idx.items()}
    
    for drug_id in tqdm(unique_drugs, desc="Computing drug enhanced embeddings"):
        if drug_id in drug_embeddings_dict and drug_id in drug_id_to_idx:
            enhanced_emb = get_enhanced_embeddings_cached(
                drug_id, 'drug', drug_embeddings_dict, drug_degrees_dict, 
                drug_id_to_idx, drug_idx_to_id, k, exp_lambda
            )
            if enhanced_emb is not None:
                enhanced_drug_cache[drug_id] = enhanced_emb
    
    _utils_logger_instance.info("Pre-computing enhanced embeddings for unique diseases...")
    enhanced_disease_cache = {}
    disease_idx_to_id = {idx: disease_id for disease_id, idx in disease_id_to_idx.items()}
    
    for disease_id in tqdm(unique_diseases, desc="Computing disease enhanced embeddings"):
        if disease_id in disease_embeddings_dict and disease_id in disease_id_to_idx:
            enhanced_emb = get_enhanced_embeddings_cached(
                disease_id, 'disease', disease_embeddings_dict, disease_degrees_dict,
                disease_id_to_idx, disease_idx_to_id, k, exp_lambda
            )
            if enhanced_emb is not None:
                enhanced_disease_cache[disease_id] = enhanced_emb
    
    # Step 6: Fast O(1) lookup for each pair
    enhanced_embeddings = []
    valid_indices = []
    
    _utils_logger_instance.info("Generating final embeddings using cached enhanced embeddings...")
    for idx, row in enumerate(tqdm(pairs_df.iter_rows(named=True), total=pairs_df.shape[0], desc="Combining cached embeddings")):
        drug_id = row['drug_id']
        dis_id = row['dis_id']
        
        # O(1) lookup instead of O(K) computation
        enhanced_drug_emb = enhanced_drug_cache.get(drug_id)
        enhanced_dis_emb = enhanced_disease_cache.get(dis_id)
        
        if enhanced_drug_emb is not None and enhanced_dis_emb is not None:
            # Combine embeddings
            combined_emb = combine_embeddings(enhanced_drug_emb, enhanced_dis_emb)
            enhanced_embeddings.append(list(combined_emb))
            valid_indices.append(idx)
        else:
            _utils_logger_instance.warning(f"Skipping pair {drug_id}-{dis_id} due to missing enhanced embeddings")
    
    _utils_logger_instance.info(f"Generated {len(enhanced_embeddings)} enhanced embeddings from {pairs_df.shape[0]} pairs")
    _utils_logger_instance.info(f"Batch optimization successful: computed {len(enhanced_drug_cache)} drug + {len(enhanced_disease_cache)} disease enhanced embeddings")
    
    return enhanced_embeddings, valid_indices


def get_enhanced_embeddings_cached(node_id, node_type, precomputed_embeddings, precomputed_degrees, 
                                  id_to_idx_map, idx_to_id_map, k=5, exp_lambda=0.7):
    """
    Optimized version of get_enhanced_embedding using pre-computed similarity matrices.
    
    Args:
        node_id: ID of the node to get enhanced embedding for
        node_type: Either 'drug' or 'disease'
        precomputed_embeddings: Dictionary of pre-computed embeddings for the node type
        precomputed_degrees: Dictionary of pre-computed degrees for the node type
        id_to_idx_map: Mapping from node ID to index in similarity matrix
        idx_to_id_map: Pre-computed reverse mapping from index to node ID
        k (int): Number of top similar nodes to use for merging
        exp_lambda (float): Lambda parameter for exponential rarity weighting
    
    Returns:
        np.ndarray or None: Enhanced embedding vector or None if node not found
    """
    # Get original embedding
    original_emb = precomputed_embeddings.get(node_id)
    
    if original_emb is None or node_id not in id_to_idx_map:
        return original_emb  # Fallback to original if available
    
    # Get index and load similarity row from disk
    node_idx = id_to_idx_map[node_id]
    
    try:
        sim_scores = load_similarity_row(node_idx, node_type)
    except FileNotFoundError:
        _utils_logger_instance.warning(f"Similarity row not found for {node_type} {node_id}, falling back to original embedding")
        return original_emb
    
    # Exclude self-similarity
    sim_scores[node_idx] = -1.0  # Set to very low value to exclude it
    
    # Get top-k similar nodes
    num_nodes = len(sim_scores)
    actual_k = min(k, num_nodes - 1)  # Exclude self
    
    if actual_k <= 0:
        return original_emb
    
    # Get indices of top-k values (sorted in descending order)
    top_k_indices = np.argpartition(sim_scores, -actual_k)[-actual_k:]
    top_k_indices = top_k_indices[np.argsort(sim_scores[top_k_indices])[::-1]]  # Sort in descending order
    top_k_values = sim_scores[top_k_indices]
    
    # Normalize similarities to get coefficients
    coef = normalize_weights(top_k_values)  # Shape: (actual_k,)
    
    # Use pre-computed idx_to_id_map (no recreation overhead)
    top_k_node_ids = [idx_to_id_map[idx] for idx in top_k_indices]
    
    # Get embeddings of top-k similar nodes
    top_k_embeddings = np.array([precomputed_embeddings[nid] for nid in top_k_node_ids])  # Shape: (actual_k, embedding_dim)
    
    # Compute weighted sum of top-k embeddings
    prototype_embedding = np.sum(coef.reshape(-1, 1) * top_k_embeddings, axis=0)  # Shape: (embedding_dim,)
    
    # Apply rarity-based weighting using pre-computed degree
    degree = precomputed_degrees.get(node_id, 0)
    rarity_coef = exponential(np.array([degree]), exp_lambda)[0]
    
    # Combine original and prototype embeddings using rarity weighting
    enhanced_embedding = (1 - rarity_coef) * original_emb + rarity_coef * prototype_embedding
    
    return enhanced_embedding


